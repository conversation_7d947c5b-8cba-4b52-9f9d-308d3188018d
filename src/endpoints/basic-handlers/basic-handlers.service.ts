import { Injectable, Logger } from '@nestjs/common';
import {
  SendMessageRequest,
  SendMessageResponse,
  WhatsAppClientStatus,
  WhatsAppMessage,
} from './interfaces/basic-handlers.interface';
import { WhatsAppService } from '../../modules/whatsapp/whatsapp.service';
import {
  WhatsAppSendMessageParams,
  WhatsAppContact,
  WhatsAppChat,
} from '../../modules/whatsapp/interfaces/whatsapp.interface';

@Injectable()
export class BasicHandlersService {
  private readonly logger = new Logger(BasicHandlersService.name);

  constructor(private readonly whatsappService: WhatsAppService) {}

  getStatus(): WhatsAppClientStatus {
    return this.whatsappService.getStatus();
  }

  getQRCode(): string | null {
    return this.whatsappService.getQRCode();
  }

  async getQRCodeWithType(responseType: 'json' | 'image'): Promise<{ type: string; data: any }> {
    if (responseType === 'image') {
      const qrBuffer = await this.whatsappService.getQRCodeAsBuffer();
      if (!qrBuffer) {
        return { type: 'json', data: { qrCode: null } };
      }
      return { type: 'image', data: qrBuffer };
    } else {
      const qrCode = this.whatsappService.getQRCode();
      return { type: 'json', data: { qrCode } };
    }
  }

  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const params: WhatsAppSendMessageParams = {
        to: request.to,
        message: request.message,
        type: request.type,
      };

      const result = await this.whatsappService.sendMessage(params);

      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      this.logger.error('Failed to send message', error);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  async restartClient(): Promise<void> {
    await this.whatsappService.restartClient();
  }

  async getChats(): Promise<WhatsAppChat[]> {
    try {
      return await this.whatsappService.getChats();
    } catch (error) {
      this.logger.error('Failed to get chats', error);
      throw error;
    }
  }

  async getMessages() {
    try {
      return await this.whatsappService.getMessages();
    } catch (error) {
      this.logger.error('Failed to get messages', error);
      throw error;
    }
  }

  async getContacts(): Promise<WhatsAppContact[]> {
    try {
      return await this.whatsappService.getContacts();
    } catch (error) {
      this.logger.error('Failed to get contacts', error);
      throw error;
    }
  }
}
