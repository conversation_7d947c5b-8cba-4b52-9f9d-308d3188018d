export interface WhatsAppSendMessageParams {
  to: string;
  message: string;
  type: 'individual' | 'group';
}

export interface WhatsAppSendMessageResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface WhatsAppContact {
  id: {
    _serialized: string;
    server: string;
    user: string;
  };
  name?: string;
  pushname?: string;
  shortName?: string;
  isBusiness?: boolean;
  isEnterprise?: boolean;
  [key: string]: any; // Allow additional properties
}

export interface WhatsAppChat {
  id: {
    _serialized: string;
    server: string;
    user: string;
  };
  name?: string;
  isGroup: boolean;
  isBusiness?: boolean;
  unreadCount: number;
  timestamp: number;
  archived: boolean;
  [key: string]: any; // Allow additional properties
}

export interface WhatsAppMessage {
  id: {
    _serialized: string;
  };
  from: string;
  to: string;
  body: string;
  fromMe: boolean;
  mentionedIds?: string[];
  [key: string]: any; // Allow additional properties
}
