# Get QR Code Endpoint

## Endpoint
```
GET /basic/qr
```

## Description
Returns the QR code needed for WhatsApp authentication when the client is not yet authenticated. The endpoint supports two response types: JSON format (default) with Data URL, or direct image format.

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| responseType | string | No | json | Response format: 'json' for JSON with Data URL, 'image' for direct PNG image |

## Response

### JSON Response (responseType=json)

#### Success Response
```json
{
  "success": true,
  "data": {
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAAAAAA..."
  }
}
```

#### Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Indicates if the request was successful |
| data | object | Container for the QR code data |
| data.qrCode | string | Base64 encoded Data URL of the QR code image |

#### No QR Code Available
```json
{
  "success": true,
  "data": {
    "qrCode": null
  }
}
```

### Image Response (responseType=image)

#### Success Response
- **Content-Type:** `image/png`
- **Content-Disposition:** `inline; filename="qr-code.png"`
- **Body:** Binary PNG image data

#### No QR Code Available
```json
{
  "success": false,
  "message": "QR code not available"
}
```
**Status Code:** 404

## Example Usage

### Get QR Code as JSON (default)
```bash
curl -X GET http://localhost:3000/basic/qr
```

### Get QR Code as JSON (explicit)
```bash
curl -X GET "http://localhost:3000/basic/qr?responseType=json"
```

### Get QR Code as Image
```bash
curl -X GET "http://localhost:3000/basic/qr?responseType=image" --output qr-code.png
```

### Display QR Code Image in Browser
```
http://localhost:3000/basic/qr?responseType=image
```

## Authentication Flow
1. Start the application
2. Call this endpoint to get the QR code
3. Display the QR code Data URL in an `<img>` tag or decode it to show the image
4. Scan the QR code using your WhatsApp mobile app
5. After successful scanning, the client will be authenticated

## Common Issues
- If `qrCode` is `null`, the client might already be authenticated
- QR codes are time-sensitive and may expire after a few minutes
- If the QR code expires, restart the client using the restart endpoint to get a new QR code